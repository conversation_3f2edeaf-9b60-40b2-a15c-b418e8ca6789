'use client'

import React from 'react';
import { TipTapEditor } from '@/common/components/molecules/tiptapEditor';
import { SocialContent } from './types';
import toast from 'react-hot-toast';
import { useProjects } from '@/common/hooks';
import { useTwitterPremium } from '@/common/hooks/useTwitterPremium';
import { PLATFORM_CHARACTER_LIMITS } from '@/common/constants';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';

interface PlatformContentTabsProps {
  platforms: SocialContent[];
  activeTab: string | null;
  onContentChange: (platformId: string, content: string) => void;
  onImageAttached?: (platformId: string, imageUrl: string, isFromAI: boolean, file?: File) => void;
  setIsImageModalOpen: (isOpen: boolean) => void;
  setActiveMediaPlatformId: (platformId: string) => void;
  handleRemoveImage: () => void;
}

export const PlatformContentTabs = ({
  platforms,
  activeTab,
  setActiveMediaPlatformId,
  setIsImageModalOpen,
  onContentChange,
  handleRemoveImage,
}: PlatformContentTabsProps) => {
  const { activeProject } = useProjects();
  const { isPremium } = useTwitterPremium();
  const { trackContentEvent } = useMixpanelEvent();
  const selectedPlatforms = platforms.filter(p => p.selected && p.connected);

  if (selectedPlatforms.length === 0) {
    return null;
  }
  const effectiveActiveTab = activeTab || selectedPlatforms[0]?.id;

  const getCharacterLimit = (platformId: string): number | undefined => {
    const platformName = platformId.toUpperCase();
    if (platformName === 'TWITTER' || platformName === 'X') {
      if (isPremium) {
        return PLATFORM_CHARACTER_LIMITS.TWITTER_PREMIUM;
      }

      return PLATFORM_CHARACTER_LIMITS.TWITTER;
    }
    if (platformName === 'LINKEDIN') {
      return PLATFORM_CHARACTER_LIMITS.LINKEDIN;
    }
    if (platformName === 'INSTAGRAM') {
      return PLATFORM_CHARACTER_LIMITS.INSTAGRAM;
    }
    if (platformName === 'FACEBOOK') {
      return PLATFORM_CHARACTER_LIMITS.FACEBOOK;
    }
    if (platformName === 'YOUTUBE') {
      return PLATFORM_CHARACTER_LIMITS.YOUTUBE;
    }
    return undefined;
  };

  const handleAIRequest = async (prompt: string, context: string) => {
    try {
      const agentId = activeProject?.accounts?.find(acc => acc.platform.toLowerCase() === activeTab?.toLowerCase() && acc.connected)?.agentId || ''
      if (!agentId) {
        console.error('Agent ID is required for AI content generation');
        return 'Error: Agent ID is missing. Please try again.';
      }

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/agents/${agentId}/improve-post-content`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          systemPrompt: prompt,
          context: context || '',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error:', response.status, errorData);
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.status === 'success' && data.post && data.post.content) {
        return data.post.content;
      } else {
        console.error('Unexpected API response format:', data);
        throw new Error('Unexpected API response format');
      }
    } catch (error) {
      console.error('Error with AI request:', error);
      toast.error("Failed to generate content");
      return '';
    }
  };
  const handleNewPostFromTopic = async (prompt: string, isKnowledge: boolean, isWebSearch: boolean) => {
    try {
      const connectedAccount = activeProject?.accounts?.find(acc => acc.connected && acc.agentId);
      const agentId = connectedAccount?.agentId || '';

      if (!agentId) {
        console.error('Agent ID is required for AI content generation');
        toast.error("No connected agents found. Please connect at least one platform.");
        return 'Error: Agent ID is missing. Please try again.';
      }

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/agents/${agentId}/generate-post-content`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          systemPrompt: prompt,
          isKnowledgeBase: isKnowledge,
          isSearchEnabled: isWebSearch,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error:', response.status, errorData);
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.status === 'success' && data.posts && Array.isArray(data.posts)) {
        let successCount = 0;
        let errorCount = 0;

        data.posts.forEach((post: { platform: string; content: string; agentId: string; error?: string }) => {
          if (post.error) {
            console.warn(`Failed to generate content for ${post.platform}:`, post.error);
            errorCount++;
            return;
          }

          const platformId = post.platform.toLowerCase();
          const matchingPlatform = selectedPlatforms.find(p =>
            p.id.toLowerCase() === platformId ||
            (platformId === 'twitter' && p.id.toLowerCase() === 'x') ||
            (platformId === 'x' && p.id.toLowerCase() === 'twitter'),
          );

          if (matchingPlatform && post.content) {
            onContentChange(matchingPlatform.id, post.content);
            successCount++;
          }
        });

        if (successCount > 1) {
          toast.success(`Content generated for ${successCount} platform${successCount > 1 ? 's' : ''}`);
        } else if (successCount === 1 && errorCount > 0) {
          toast.success(`Content generated for 1 platform (${errorCount} failed)`);
        }

        if (successCount > 0) {
          trackContentEvent('content', {
            platform: data.posts.find((p: any) => !p.error)?.platform,
            prompt,
            contentLength: data.posts.reduce((total: number, post: any) =>
              total + (post.content ? post.content.replace(/<[^>]*>/g, '').length : 0), 0,
            ),
          });
        }

        const currentPlatformPost = data.posts.find((post: { platform: string; content: string }) => {
          const platformId = post.platform.toLowerCase();
          const activeTabLower = activeTab?.toLowerCase();
          return platformId === activeTabLower ||
                 (platformId === 'twitter' && activeTabLower === 'x') ||
                 (platformId === 'x' && activeTabLower === 'twitter');
        });

        if (currentPlatformPost && currentPlatformPost.content) {
          return currentPlatformPost.content;
        }

        // If no content found for current tab, return the first available content
        const firstValidPost = data.posts.find((post: { content: string; error?: string }) =>
          post.content && !post.error,
        );
        return firstValidPost?.content || 'Content generated for other platforms. Please check other tabs.';

      } else if (data.status === 'success' && data.post && data.post.content) {
        // Handle legacy single-platform response format for backward compatibility
        return data.post.content;
      } else {
        console.error('Unexpected API response format:', data);
        throw new Error('Unexpected API response format');
      }
    } catch (error) {
      console.error('Error with AI request:', error);
      toast.error("Failed to generate content");
      return '';
    }
  };
  const handleImageButtonClick = (platformId: string) => {
    if (setActiveMediaPlatformId && setIsImageModalOpen) {
      console.log(platformId)
      setActiveMediaPlatformId(platformId);
      setIsImageModalOpen(true);
    }
  };

  return (
    <div className="mb-2">
      {selectedPlatforms.map(platform => {
        const isActive = platform.id === effectiveActiveTab;
        return isActive && (
          <div key={platform.id}>
            <TipTapEditor
              initialContent={platform.content}
              placeholder={`Write your own post...`}
              onChange={(html) => onContentChange(platform.id, html)}
              onAIRequest={handleAIRequest}
              onNewPostRequest={handleNewPostFromTopic}
              onImageButtonClick={() => handleImageButtonClick(platform.id)}
              attachedImages={platform.attachments || []}
              handleRemoveImage={handleRemoveImage}
              characterLimit={getCharacterLimit(platform.id)}
            />
          </div>
        );
      })}
    </div>
  );
};
