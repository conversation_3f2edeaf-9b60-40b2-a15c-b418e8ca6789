'use client'

import { Button } from '@/common/components/atoms';
import { motion } from "framer-motion";
import Link from 'next/link';
import { routes } from '@/common/routes';
import { useSupabaseAuth } from '@/common/hooks';
import { secondaryFont } from '@/common/utils/localFont';
import { ClientLogo } from '../header/ClientLogo';
import { UserDropdown } from '../../molecules';

interface CanvasHeaderProps {
  onSaveDesign: () => void;
}

export const CanvasHeader = ({
  onSaveDesign,
}: CanvasHeaderProps) => {
  const {
    isAuthenticated, user, signOut,
  } = useSupabaseAuth()
  return (
    <div className="bg-eerie-black border-b border-neutral-700 px-4 py-2 md:py-3 flex items-center justify-between relative">
      <div className="flex items-center gap-1 md:gap-2">
        <Link href={routes.homePath} prefetch={true} replace className="flex gap-1 md:gap-2 items-center text-white font-semibold text-xl md:text-2xl">
          <ClientLogo width={24} height={24}/>
          <motion.span
            initial={{
              opacity: 0,
              translateX: 20,
            }}
            animate={{
              opacity: 1,
              translateX: 0,
            }}
            transition={{
              duration: 0.5,
              delay: 1,
            }}
            className={`text-white ${secondaryFont.className} animate-glowTransition`}
          >
            Media Pilot
          </motion.span>
        </Link>
      </div>
      <div className="flex items-center gap-1 md:gap-2">
        <Button
          variant="gradient"
          size="sm"
          type="button"
          onClick={onSaveDesign}
        >
          Save to Post
        </Button>
        <>
          <motion.div initial={{
            opacity: 0,
          }}
          animate={{
            opacity: 1,
          }}
          transition={{
            duration: 0.5,
          }} className="flex gap-4 items-center">
            <div className="xl:hidden flex gap-2">
              {isAuthenticated ? (
                <UserDropdown user={user} signOut={signOut} />
              ) : (
                <>
                </>
              )}
            </div>
          </motion.div>
          <motion.div className="hidden xl:flex lg:mr-0 gap-2 sm:gap-2 items-center"
            initial={{
              opacity: 0,
            }}
            animate={{
              opacity: 1,
            }}
            transition={{
              duration: 0.5,
            }}
          >
            {isAuthenticated ? (
              <UserDropdown user={user} signOut={signOut} />
            ) : (
              <>
              </>
            )}
          </motion.div>
        </>
      </div>
    </div>
  );
};
